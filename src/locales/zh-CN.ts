export default {
  'hooks.Authorization.loginFailedMessage': '登录失败',
  'service.Me.anonymousName': '路人',
  'Common.loadingText': '系统正在操作，请稍候...',
  'File.file': '文件',
  'File.document': '文档',
  'File.newdoc': '文档',
  'File.modoc': '传统文档',
  'File.mosheet': '表格',
  'File.table': '应用表格',
  'File.presentation': '幻灯片',
  'File.form': '表单',
  'File.normalForm': '普通表单',
  'File.tableViewForm': '表格视图表单',
  'File.quizForm': '测验表单',
  'File.folder': '文件夹',
  'Login.loginTitle': '登录',
  'Login.autoLanguageSetting': '跟随系统',
  'Login.autoThemeSetting': '跟随系统',
  'Login.lightThemeSetting': '亮色模式',
  'Login.darkThemeSetting': '暗色模式',
  'LoginView.userNameInputPlaceholder': '输入邮箱',
  'LoginView.passwordInputPlaceholder': '输入密码',
  'LoginView.loginButtonText': '登录',
  'LoginView.userNameInputLabel': '邮箱',
  'LoginView.passwordInputLabel': '密码',
  'LoginView.emailFormatInaccurate': '邮箱格式不准确',
  'MessageCenter.onlyreadButtonText': '只看已读',
  'MessageCenter.onlyUnreadButtonText': '只看未读',
  'MessageCenter.allMarkReadButtonText': '全部标记未已读',
  'UserCenter.settings': '个人设置',
  'UserCenter.myBusiness': '我的企业',
  'UserCenter.switchLanguages': '界面语言',
  'UserCenter.logOut': '退出登陆',
  'UserCenter.myDesktopCapacit': '我的桌面容量',
  'UserCenter.totalEnterpriseCapacity': '企业总容量',
  'SearchCenter.me': '我',
  'SearchCenter.update': '更新',
  'SearchCenter.open': '打开',
  'SearchCenter.searchFile': '搜索文件',
  'SearchCenter.noData': '无搜索结果',
  'SearchCenter.used': '最近使用',
  'SearchCenter.search': '搜索结果',
  'Header.backButtonTipText': '返回',
  'Header.backToButtonTipText': '返回到',
  'Header.createButtonTipText': '新建',
  'Header.inputPlaceholder': '无标题',
  'Header.teamButtonText': '协作',
  'Header.shareButtonText': '分享',
  'Header.historyButtonText': '历史',
  'Header.demoButtonText': '演示',
  'Header.fileMenuButtonTipText': '文件菜单',
  'Editor.saveStatus.offlineSaving': '正在保存离线内容',
  'Editor.saveStatus.offlinePersistSucceed': '编辑已离线保存，连接到网络后会自动同步',
  'Editor.saveStatus.offline': '未连接到网络，内容将离线保存',
  'Editor.saveStatus.offlinePersistFailed': '离线保存失败',
  'Editor.saveStatus.online': '内容将自动保存',
  'Editor.saveStatus.onlineSaving': '正在保存',
  'Editor.saveStatus.saveAccepted': '正在保存',
  'Editor.saveStatus.saveSucceed': '自动保存成功',
  'Editor.saveStatus.applySucceed': '内容已自动更新',
  'Editor.saveStatus.saveFailed': '保存失败',
  'Editor.saveStatus.applyFailed': '保存失败',
  'Editor.saveStatus.saveTimeout': '保存失败',
  'Editor.saveStatus.acceptTimeout': '保存失败',
  'Editor.syncStatus.syncSaving': '正在同步离线数据，请稍等...',
  'Editor.syncStatus.syncSucceed': '离线数据同步成功，内容将自动保存',
  'Editor.syncStatus.syncFailed': '保存失败',
  'Editor.noSupport': '该功能暂不支持',
  'Editor.ok': '知道了',
  'Editor.syncSaving': '正在同步离线数据，请稍等...',
  'Editor.syncSucceed': '离线数据同步成功。',
  'Editor.syncFailed': '离线数据同步失败。',
  'Editor.noEditing': '系统已禁止编辑',
  'Editor.noEditingContent': '您没有该文档的编辑权限，请联系文件管理员',
  'Editor.sorry': '抱歉...',
  'Editor.fileDeleted': '文件已被删除',
  'Editor.saveFailed': '保存失败',
  'Editor.saveFailedContent': '保存失败，请复制当前编辑数据，刷新后继续使用',

  'Header.favorite': '收藏',
  'Header.unfavorite': '取消收藏',
  'Header.favorited': '已收藏',
  'Header.unfavorited': '已取消收藏',
  'BackToPopover.searchFiles': '搜索文件...',
  'BackToPopover.backTo': '返回到',
  'BackToPopover.myDesktop': '我的桌面',
  'BackToPopover.workbench': '工作台',
  'BackToPopover.recentlyUsed': '最近使用',
  'BackToPopover.quickAccess': '快速访问',
  'AvatarGroup.restCount': '还有{count}个协作者',
  'SiderMenu.siderMenuCreactText': '创建',
  'SiderMenu.siderMenuRecentText': '最近文件',
  'SiderMenu.siderMenuShareText': '共享给我',
  'SiderMenu.siderMenuFavoritesText': '我的收藏',
  'SiderMenu.siderMenuDesktopText': '我的桌面',
  'SiderMenu.siderMenuSpaceText': '团队空间',
  'SiderMenu.siderMenuTrashText': '回收站',
  'SiderMenu.siderMenuBusinessText': '企业管理',
  'SiderMenu.siderMenuCreateDocText': '文档',
  'SiderMenu.siderMenuCreateMoDocText': '传统文档',
  'SiderMenu.siderMenuCreateTableText': '表格',
  'SiderMenu.siderMenuCreateMoTableText': '应用表格',
  'SiderMenu.siderMenuCreatePptText': '幻灯片',
  'SiderMenu.siderMenuCreateFormText': '表单',
  'SiderMenu.siderMenuCreateOrdinaryFormText': '普通表单',
  'SiderMenu.siderMenuCreateTableFormText': '表格视图表单',
  'SiderMenu.siderMenuCreateTestFormText': '测验表单',
  'SiderMenu.siderMenuCreateFolderText': '文件夹',
  'SiderMenu.siderMenuCreateSpaceText': '团队空间',
  'SiderMenu.siderMenuCreateUploadFolderText': '上传文件夹',
  'SiderMenu.siderMenuCreateUploadFileText': '上传文件',
  'SiderMenu.siderMenuCreateTemplateText': '从模版库创建',
  'CreateFileMenu.networkStatusTipText': '请检查您的网络',
  'CreateWithNamePop.folderTitle': '新建文件夹',
  'CreateWithNamePop.saveVersionTitle': '保存版本',
  'CreateWithNamePop.spaceTitle': '新建团队空间',
  'CreateWithNamePop.folderPlaceholder': '请输入文件夹名称',
  'CreateWithNamePop.spacePlaceholder': '请输入空间名称',
  'CreateWithNamePop.saveVersionPlaceholder': '请输入保存版本名称',
  'CreateWithNamePop.ruleMessage': '请填写完整信息',
  'Editor.openInNewTab': '在新标签页打开',
  'AddNewPopover.templateLibrary': '模板库',
  'AddNewPopover.upload': '上传',
  'AddNewPopover.uploadFolder': '上传文件夹',
  'Error.loginGuideTitle': '请登录后访问',
  'Error.loginText': '登录',
  'Error.file404Title': '您访问的页面不存在',
  'Error.goBackHomePage': '返回首页',
  'Error.fileDeleteTitle': '出错',
  'Error.fileDeleteSubTitle': '{fileName} 已删除',
  'Error.fileNoPermissionTitle': '没有权限访问',
  'Error.fileNoPermissionSubTitle': '当前登录账号 {name} 没有权限访问这个{fileName}',
  'Error.switchAccount': '切换账号',
  'Error.accessRestrictedTitle': '需要登录访问',
  'Error.noSeatsTitle': '您尚未获得该套件席位，请联系管理员分配或购买席位',
  'Error.unknownErrorTitle': '页面未成功加载',
  'Error.unknownErrorSubTitle': '请确认网络良好的状态下，刷新页面重试',
  'deleteConfirm.title': '确认删除',
  'deleteConfirm.content': '确认删除文件？删除后所有协作者都无法访问此文件。',
  'deleteConfirm.cancel': '取消',
  'deleteConfirm.success': '删除成功',
  'deleteConfirm.error': '删除失败',
  'useFileDetail.creator': '创建者',
  'useFileDetail.modocTitle': '文档信息',
  'useFileDetail.mosheetTitle': '表格信息',
  'useFileDetail.tableTitle': '应用表格信息',
  'useFileDetail.pptTitle': '幻灯片信息',
  'useFileDetail.formTitle': '表单信息',
  'useFileDetail.statisticWordCount': '总字数',
  'useFileDetail.charCount': '字符数（计空格）',
  'useFileDetail.charCountWithoutSpaces': '字符数（不计空格）',
  'useFileDetail.page': '页数',
  'useFileDetail.total': '总字数',
  'useFileDetail.paragraphCount': '段落数',
  'useFileDetail.sections': '节数',
  'useFileDetail.views': '阅读次数',
  'useFileDetail.times': '次',
  'RenameModal.edit': '修改',
  'RenameModal.editSuccess': '修改成功',
  'RenameModal.editError': '修改失败',
  'RenameModal.title': '文件重命名',
  'RenameModal.InputPlaceholder': '请输入文件名称',
  'RenameModal.validatorMessage': '文件名中不得包含以下非法字符',
  'formatTime.justNow': '刚刚',
  'formatTime.minutesAgo': '{minutes} 分钟前',
  'formatTime.today': '今天 {hhmm}',
  'formatTime.yesterday': '昨天 {hhmm}',
  'File.setFilter': '设置筛选',
  'File.clearTrash': '清空回收站',
  'File.clearTrashSuccess': '清空回收站成功',
  'File.clearTrashError': '清空回收站失败',
  'File.clearTrashWarn': '是否确认清空回收站，确认将无法找回，请谨慎操作！',
  'File.clearTrashTips': '温馨提示：回收站内的文件也属于企业的数字资产，企业有权进行回收',
  'File.resetFirst': '文件已恢复至原位置',
  'File.resetError': '恢复失败',
  'File.checkedTotal': '已选中 {checked} 个文件',
  'File.fileName': '文件名',
  'File.createName': '创建者',
  'File.updateTime': '最近更新',
  'File.updatedAt': '更新时间',
  'File.createdAt': '创建时间',
  'File.openTime': '打开时间',
  'File.editTime': '编辑时间',
  'File.fileSize': '大小',
  'File.uncheck': '取消选中',
  'File.allCheck': '全选',
  'File.move': '移动',
  'File.delete': '删除',
  'File.more': '更多',
  'File.deleteSuccess': '删除成功',
  'File.deleteError': '删除失败',
  'File.deleteTips': '批量删除文件上限为{max}个',
  'File.newTabOpens': '新标签页打开',
  'File.edit': '编辑',
  'File.star': '添加到我的收藏',
  'File.starSuccess': '添加到我的收藏成功',
  'File.starError': '添加到我的收藏失败',
  'File.removeStar': '从我的收藏移除',
  'File.removeSuccess': '从我的收藏移除成功',
  'File.removeError': '从我的收藏移除失败',
  'File.share': '分享',
  'File.view': '预览',
  'File.shareInfo': '共享信息',
  'File.collaboration': '协作',
  'File.download': '下载',
  'File.downloadSuccess': '下载成功',
  'File.downloadError': '下载失败',
  'File.png': '图片',
  'File.reName': '重命名',
  'File.moveTo': '移动到',
  'File.copyTo': '创建副本到',
  'File.clearRecord': '清空此条记录',
  'File.clearFilter': '清除筛选',
  'File.recentlyOpened': '最近打开',
  'File.recentlyEdit': '最近编辑',
  'File.deleteTime': '删除时间',
  'File.recover': '恢复文件',
  'File.deleteCompletely': '彻底删除',
  'File.noShareTitle': '还没有收到任何协作邀请',
  'File.noShareDescription': '你可以在这里看到你被添加成为协作者的文件和文件夹',
  'File.noFavoritesTitle': '暂无收藏',
  'File.noFavoritesDescription': '你可以在这里看到你收藏的文件和文件夹',
  'File.noDesktopTitle': '还没有创建任何文件',
  'File.noDesktopDescription': '你可以在这里看到自己创建的文件',
  'File.noTrashTitle': '还没有任何垃圾和文件',
  'File.noTrashDescription': '你可以在这里看到被删除的文件和文件夹，这里的文件和文件夹都可以恢复到原始位置',
  'MessageCenter.commented': '评论了',
  'MessageCenter.mentioned': '提到了你',
  'MessageCenter.addCollaborator': '添加你为协作者',
  'MessageCenter.setAdministrator': '将你设置为企业管理员',
  'MessageCenter.inviteJoinBusiness': '邀请你加入企业',
  'MessageCenter.newMembersJoin': '新成员加入',
  'MessageCenter.deleteDocument': '删除文档',
  'MessageCenter.remindsReviewTasks': '提醒你查看任务',
  'MessageCenter.liked': '点赞了',
  'MessageCenter.NotificationToDoChanges': '删除了你在',
  'MessageCenter.dateArrived': '日期到了',
  'MessageCenter.mentionYou': '提到你',
  'MessageCenter.moveYouOfBusiness': '将你移出企业',
  'MessageCenter.handingOverBusinessToYou': '将企业移交给你',
  'MessageCenter.companyNameChanged': '修改了企业名称“{name}”',
  'MessageCenter.openedBusinessLink': '评论打开企业邀请链接了',
  'MessageCenter.closedBusinessLink': '关闭了企业邀请链接',
  'MessageCenter.taskReminders': '任务提醒',
  'MessageCenter.tableSelectionReminders': '表格选区提醒',
  'MessageCenter.changeUserConfig': '修改企业成员账号（邮箱，昵称，密码）',
  'MessageCenter.systemNotifications': '系统通知',
  'MessageCenter.application': '申请',
  'MessageCenter.markRead': '标记为已读',
  'MessageCenter.join': '加入',
  'MessageCenter.discussion': '的讨论',
  'MessageCenter.permissions': '权限',
  'MessageCenter.basicInformationModification': '基本信息修改',
  //团队空间
  'Space.countSpace': '个空间',
  'Space.createSpace': '新建空间',
  'Space.createTeamSpace': '新建团队空间',
  'Space.sure': '确定',
  'Space.cancel': '取消',
  'Space.enterSpaceName': '请输入空间名称',
  'Space.SpaceNameHeader': '空间名称',
  'Space.infoSuccess': '创建成功',
  'Space.infoEmpty': '空间名称不能为空',
  'Space.infoWaring1': '开头不能有空格，已自动去除',
  'Space.infoWaring2': '空间名称不能超过20个字符',
  'Space.rightClickShare': '分享',
  'Space.rightClickCollaboration': '协作',
  'Space.rightClickSetting': '设置',
  'Space.rightClickDelete': '删除',
  'Space.infoEditSuccess': '修改成功',
  'Space.teamspaceSetting': '团队空间设置',
  'Space.teamspaceOwnership': '所属权',
  'Space.teamspaceWhatOwnership': '所属权是什么?',
  'Space.teamspaceBelongsto': '该空间的所有权归属于',
  'Space.teamspaceOwnershipExp':
    '所有权决定了团队空间里的文件被谁管理。如果所有权归属于企业，则会受到企业安全设置的影响，被审计系统收录，并参入效能看板的统计。',
  'Space.teamspaceConfirmDeletion': '确认删除',
  'Space.teamspaceDeleteSuccess': '删除成功',
  'Space.teamspaceDeleteTipText': '确认删除团队空间吗？删除后，所有协作者都无法访问此团队空间',
  'Space.noTeamspace': '暂无团队空间',
  'Space.noTeamspaceTipText': '点击左上角新建按钮为你的团队新建一个空间',
  //个人设置
  'Profile.title': '个人设置',
  'Profile.accountInfo': '账号信息',
  'Profile.preferenceSitting': '偏好设置',
  'Profile.accountID': '账号 ID',
  'Profile.modifyInfo': '修改基本信息',
  'Profile.safetySetting': '安全设置',
  'Profile.accountPd': '密码',
  'Profile.modify': '修改',
  'Profile.modifyImg': '修改头像',
  'Profile.uploadImg': '上传头像',
  'Profile.nickName': '昵称',
  'Profile.enterNickname': '请输入昵称',
  'Profile.nickNameRule': '昵称不能超过20个字符',
  'Profile.modifySuccess': '修改用户信息成功',
  'Profile.modifyFailed': '修改用户信息失败',
  'Profile.getUploadToken': '获取上传图片token失败',
  'Profile.uploadImgSuccess': '上传图片成功',
  'Profile.uploadImgFailed': '上传图片失败',
  'Profile.changePd': '修改密码',
  'Profile.forgetPd': '忘记密码',
  'Profile.currentPassword': '当前密码',
  'Profile.newPassword': '设置密码',
  'Profile.confirmNewPassword': '确认新密码',
  'Profile.currentPasswordRequired': '必须填写当前密码',
  'Profile.currentPasswordPlaceholder': '请输入当前密码',
  'Profile.newPasswordPlaceholder': '请输入新密码',
  'Profile.newPasswordRequired': '输入新密码',
  'Profile.newPasswordLength8': '密码不能少于 8 个字符',
  'Profile.newPasswordRule': '密码必须包含数字、英文大写和小写字母',
  'Profile.confirmNewPasswordPlaceholder': '请再次输入新密码',
  'Profile.confirmNewPasswordRequired': '请确认新密码',
  'Profile.confirmNewPasswordMatch': '两次输入的密码不一致',
  'Profile.changePdSuccess': '修改密码成功',
  'Profile.changePdFailed': '修改密码失败',
  'Profile.uploadImgRuleType': '只能上传 JPG、PNG、GIF、JPEG 格式的图片',
  'Profile.uploadImgRuleSize': '图片大小不得超过 2MB',
  'Profile.networkError': '网络响应异常',
  'ManagementSiderMenu.backDesktopTest': '返回主页',
  'FileMenuPopover.favorite': '收藏',
  'FileMenuPopover.move': '移动',
  'FileMenuPopover.createCopy': '创建副本',
  'FileMenuPopover.download': '下载',
  'FileMenuPopover.print': '打印',
  'FileMenuPopover.saveVersion': '保存版本',
  'FileMenuPopover.viewHistory': '查看历史',
  'FileMenuPopover.viewCommentList': '查看评论列表',
  'FileMenuPopover.help': '帮助',
  'FileMenuPopover.docGuide': '文档使用指南',
  'FileMenuPopover.docShortcut': '文档快捷键',
  'FileMenuPopover.mosheetGuide': '表格使用指南',
  'FileMenuPopover.mosheetShortcut': '表格快捷键',
  'FileMenuPopover.delete': '删除',
  'FileMenuPopover.downImage': '图片',
  'FileMenuPopover.downWord': 'Word',
  'FileMenuPopover.downPDF': 'PDF',
  'FileMenuPopover.downMarkdown': 'Markdown',
  'FileMenuPopover.downWPS': 'WPS',
  'FileMenuPopover.downImagePDF': '纯图PDF',
  'FileMenuPopover.downExcel': 'Excel',
  'FileMenuPopover.downZip': 'ZIP',
  'FileMenuPopover.downPPTX': 'PPTX',
  'FileMenuPopover.convertToMoSheet': '转换为石墨表格',
  'FileMenuPopover.downToExcel': '下载为Excel',
  'FileMenuPopover.tableHelp': '应用表格帮助中心',
  'FileMenuPopover.addComment': '添加评论',
  'FileMenuPopover.viewComment': '查看评论',
  'FileMenuPopover.formHelp': '表单帮助中心',
  'FileMenuPopover.noPermissionTip': '无权限操作，请联系文件管理者',

  'UploadBoard.uploadSuccessTitle': '上传完成',
  'UploadBoard.uploadingTitle': '正在上传',
  'UploadBoard.uploadFailTitle': '发生错误',
  'UploadBoard.uploadCancelTitle': '项已取消',
  'UploadBoard.uploadConfirmCancelTitle': '确定取消上传？',
  'UploadBoard.uploadConfirmCancelContent': '关闭面板将取消正在上传的文件，已上传成功的文件不受影响。确定要取消上传？',
  'UploadBoard.uploadConfirmCancelOkText': '取消上传',
  'UploadBoard.uploadConfirmCancelCancelText': '继续上传',
  'UploadBoard.uploadFailTipTitleText': '部分文件上传失败，请重新上传或复制以下错误信息提交客服处理:',
  'UploadBoard.uploadFailMessageText': '错误信息',
  'UploadBoard.uploadCopyFailMessageText': '复制错误信息',
  'UploadBoard.uploadCopySuccessText': '复制成功',
  'UploadBoard.uploadCheckFailMessageText': '查看错误信息',
  'UploadBoard.uploadRetryText': '重试',
  'UploadBoard.uploadOpenFolderText': '打开所在文件夹',
  'UploadBoard.uploadStatusTipCancelText': '已取消',
  'UploadBoard.uploadExpTipRetractText': '收起',
  'UploadBoard.uploadExpTipExpandText': '展开',
  'UploadBoard.uploadExpTipRetractErrorMessageText': '收起错误信息',
  'UploadBoard.failTipNum': '{number}个',
  'UploadBoard.failTipTitle': '检测到未知错误，以下共{number}条文件请单独上传或复制错误信息提交客服处理:',
  'UseFileUpload.networkError': '网络异常',
  'UseFileUpload.noSpaceTitle': '空间内存不足',
  'UseFileUpload.noSpaceContent': '您的空间内存不足，请联系客服或销售来增购空间容量；',
  'UseFileUpload.noSpaceOkText': '确定',

  // 企业管理
  'Management.backDesktop': '返回主站',
  'Management.enterpriseManagementSystem': '企业管理系统',
  'Management.businessID': '企业 ID',
  'Management.companyName': '企业名称',
  'Management.workingDays': '工作日',
  'Management.performance': '效能看板',
  'Management.board': '效能看板',
  'Management.memberList': '通讯录',
  'Management.auditLog': '操作日志',
  'Management.kitValuePack': '套件增值包',
  'Management.onlineSeatWhiteList': '在线席位白名单',
  'Management.settings': '企业设置',
  'FilePathPicker.createTitle': '选择创建位置',
  'FilePathPicker.moveTitle': '选择移动位置',
  'FilePathPicker.fileName': '文件名',
  'FilePathPicker.createIn': '创建在',
  'FilePathPicker.moveTo': '移动到',
  'FilePathPicker.move': '移动',
  'FilePathPicker.copy': '副本',
  'FilePathPicker.selectPlaceholder': '请选择',
  'FilePathPicker.createFolder': '新建文件夹',
  'FilePathPicker.moveFailed': '移动失败',
  'FilePathPicker.noSupportTip': '当前位置不支持创建文件夹',
  'FilePathPicker.files': '文件',
  'FilePathPicker.folders': '文件夹',
  'FilePathPicker.noPermissionTip': '当前文件夹无编辑权限，无法新建{type}，请联系该文件夹管理者',
  'FilePathPicker.noMoveToTargetLocationTip': '移动失败，无移动到目标位置的权限',
  'FilePathPicker.noMoveFilePermissionTip': '无移动该文件的权限，仅文件管理者可移动',
  'FilePathPicker.createPlaceHolder': '请为在「{folderName}」中创建的文件夹命名',
  'FilePathPicker.createFolderSuccess': '新建文件夹成功',
  'FilePathPicker.createFolderFailed': '新建文件夹失败',
  'FilePathPicker.cancel': '取消',
  'FilePathPicker.confirm': '确定',
  'FilePathPicker.createCopy': '创建副本',
  'FilePathPicker.quickEntry': '快捷入口',
  'FilePathPicker.desktopSpace': '桌面与空间',
  'FilePathPicker.recent': '最近位置',
  'FilePathPicker.shared': '共享给我',
  'FilePathPicker.favorites': '我的收藏',
  'FilePathPicker.desktop': '我的桌面',
  'FilePathPicker.space': '团队空间',
  'FilePathPicker.createSuccess': '创建成功，副本保存到「{folderName}」',
  'FilePathPicker.moveSuccess': '移动成功',
  'FilePathPicker.emptyUsedTitle': '还没有移动过文件',
  'FilePathPicker.emptyUsedSubTitle': '你可以在这看到最近移动到的目的地文件夹',
  'FilePathPicker.emptySharedTitle': '还没有收到任何文件夹的共享',
  'FilePathPicker.emptySharedSubTitle': '这里可以看到被添加成为协作者的文件夹列表',
  'FilePathPicker.emptyFavoritesTitle': '暂无收藏的文件夹',
  'FilePathPicker.emptyFavoritesSubTitle': '这里可以看到你收藏的文件夹列表',
  'FilePathPicker.emptySpaceTitle': '还没有团队空间',
  'FilePathPicker.emptySpaceSubTitle': '这里可以看到有权编辑的团队空间列表',
  'FilePathPicker.noSearchResult': '无搜索结果',
  'FilePathPicker.searchPlaceholder': '请输入关键字',

  'TemplateReview.useTemp': '使用此模版',
  'TemplateReview.back': '返回',
  'TabContent.preview': '预览',
  'TabContent.use': '使用',
  'TabContent.testFormSubTitle': '校验答案/设置分数',
  'TabContent.tableFormSubTitle': '批量填写反馈',
  'TabContent.formSubTitle': '信息收集/问卷调查',
  'TabContent.pptSubTitle': '工作汇报/商业演讲',
  'TabContent.tableSubTitle': '任务管理/信息录入',
  'TabContent.sheetSubTitle': '工作汇报/商业演讲',
  'TabContent.docxSubTitle': '合同通知/规范书记',
  'TabContent.docSubTitle': '快速书记/文本编辑',
  'TabContent.empty': '空白',
  //分享协作
  'ShareCollaboration.title': '分享协作',
  'ShareCollaboration.copySuccess': '链接已复制',
  'ShareCollaboration.copyFail': '复制失败',
  'ShareCollaboration.back': '返回',
  'ShareCollaboration.add': '添加',
  'ShareCollaboration.coauthor': '协作者',
  'ShareCollaboration.admin': '管理者',
  'ShareCollaboration.noCollaborator': '暂无',
  'ShareCollaboration.linkShare': '链接分享',
  'ShareCollaboration.shareMethod': '分享方式',
  'ShareCollaboration.qrCodeShare': '扫码分享',
  'ShareCollaboration.copyLink': '复制链接',
  'ShareCollaboration.accessPassword': '访问密码',
  'ShareCollaboration.setPermission': '设置权限',
  'ShareCollaboration.linkReadOnly': '互联网上获得链接的人只能阅读',
  'ShareCollaboration.linkInCompany': '企业内获得链接的人',
  'ShareCollaboration.readOnly': '只能阅读',
  'ShareCollaboration.comment': '可以评论',
  'ShareCollaboration.commentAndEdit': '可以评论并编辑',
  'ShareCollaboration.linkInInternet': '互联网上获得链接的人',
  'ShareCollaboration.linkInCompanyWithPassword': '企业内获取链接和密码的人',
  'ShareCollaboration.linkInternetWithPassword': '互联网上获得链接和密码的人',
  'ShareCollaboration.day': '天',
  'ShareCollaboration.searchAddCollab': '点击此处搜索并添加协作者',
  'ShareCollaboration.open': '已开启',
  'ShareCollaboration.close': '未开启,文件的协作者和管理者仍可访问',
  'ShareCollaboration.linkWithPassword': '和密码',
  'ShareCollaboration.linkPassword': '链接密码',
  'ShareCollaboration.changePassword': '更换密码',
  'ShareCollaboration.needPassword': '需要密码访问',
  'ShareCollaboration.linkExpiration': '链接有效期',
  'ShareCollaboration.switchOff': '开关关闭：链接将持续有效',
  'ShareCollaboration.switchOn': '开关打开：设置的有效期结束后，链接自动失效',
  'ShareCollaboration.expirationClose': '有效期关闭，链接永久有效',
  'ShareCollaboration.remaining': '剩余',
  'ShareCollaboration.inheritPermission': '继承权限',
  'ShareCollaboration.forbidAccess': '禁止访问',
  'ShareCollaboration.removePermission': '移除权限',
  'ShareCollaboration.modifySuccess': '修改成功',
  'ShareCollaboration.deleteSuccess': '删除成功',
  'ShareCollaboration.addCoauthor': '添加协作者',
  'ShareCollaboration.onlyManagerCanAddCoauthor': '只有管理者才能添加协作者',
  'ShareCollaboration.onlyManagerCanAddManager': '只有管理者才能添加管理者',
  'ShareCollaboration.parentCoauthor': '上级目录协作者',
  'ShareCollaboration.collapse': '收起',
  'ShareCollaboration.expand': '展开',
  'ShareCollaboration.addManager': '添加管理者',
  'ShareCollaboration.removeManager': '移除管理者权限',
  'ShareCollaboration.removeManagerSuccess': '移除部门管理者权限成功',
  'ShareCollaboration.removeManagerSuccess2': '移除人员管理者权限成功',
  'ShareCollaboration.addSuccess': '添加成功',
  'ShareCollaboration.removeSuccess': '移除管理者权限成功',
  'ShareCollaboration.setManager': '设置为管理者',
  'ShareCollaboration.addPermission': '添加权限',
  'ShareCollaboration.deleteDepartmentSuccess': '部门删除成功',
  'ShareCollaboration.deleteUserSuccess': '人员删除成功',
  'ShareCollaboration.operationSuccess': '操作成功',
  'ShareCollaboration.recent': '最近联系',
  'ShareCollaboration.organization': '组织架构',
  'ShareCollaboration.clickHereToSearchAndAdd': '点击此处搜索并添加',
  'ShareCollaboration.searchResult': '搜索结果',
  'ShareCollaboration.sendNotificationToTheOther': '添加协作者/管理者时，向对方发送通知',
  // 请求错误
  'Request.noAuth': '未认证，请重新登录',
  'Request.noFile': '文件不存在',
  'Request.notFound': '页面不存在',
  'Request.fileDeleted': '文件已被删除',
  'Request.noAuthorization': '没有访问权限',
  'Request.forbidden': '权限不足',
  'Request.netError': '网络异常',
  'Request.noSeats': '无可用固定席位',
  'Request.default': '请求失败，请稍后再试',
  'Request.notFoundUser': '找不到该用户',
};
