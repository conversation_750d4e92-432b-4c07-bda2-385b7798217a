import type { AxiosError, AxiosInstance } from 'axios';
import axios from 'axios';

import { fm2 } from '@/modules/Locale';
import type { ErrorData } from '@/types/api';
import to from '@/utils/await-to';
import { ErrorCodeMap } from '@/utils/request/ErrorCodeMap';

const CommonApi = axios.create({ baseURL: '/api/v1' });

const addInterceptors = (instance: AxiosInstance) => {
  instance.interceptors.response.use(undefined, async (error: AxiosError & { __fromCache?: any }) => {
    const { response, __fromCache } = error;
    // 如果是我们主动 reject 的缓存命中，直接返回缓存内容作为模拟响应
    if (__fromCache) {
      return Promise.resolve({ data: __fromCache, config: error.config, status: 200 });
    }

    if (response) {
      // backend handled error
      const errorData = response.data as ErrorData;
      const responseError = {
        ...response,
        data: {
          ...errorData,
          msg: fm2(ErrorCodeMap[errorData.code] || 'Request.default'),
        },
      };
      return Promise.reject(responseError);
    }

    const responseError = {
      ...error,
      data: {
        code: -1,
        msg: fm2('Request.default'),
        tid: '',
      },
    };
    return Promise.reject(responseError);
  });

  instance.interceptors.request.use((config) => {
    const requestUrl = `${config.baseURL}${config.url}`;
    const prefetchPromise = window.__DRIVE_PREFETCH__[requestUrl];
    if (prefetchPromise) {
      return prefetchPromise.then(
        (data) => {
          delete window.__DRIVE_PREFETCH__[requestUrl];
          return Promise.reject({
            __fromCache: data,
            config,
            data: requestUrl,
          });
        },
        () => {
          // 如果数据预取的时候失败了，这里就不做任何处理，这样这个请求就会正常发送出去
          return config;
        },
      );
    }
    return config;
  });
};

addInterceptors(CommonApi);

// 处理需要返回错误的情况
const catchApiResult = <T>(fn: Promise<T>) => {
  return to<T, ErrorData>(fn);
};

export { catchApiResult, CommonApi };
