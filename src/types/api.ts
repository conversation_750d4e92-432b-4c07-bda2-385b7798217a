import type { AxiosError } from 'axios';

/**
 * API响应类型定义
 */
export interface ApiResponse {
  status?: number;
  data?: Record<string, unknown> | string; // data 可能是对象或字符串
}

/**
 * 文件详情数据结构
 */
export interface FileDetail {
  guid: string;
  id: string;
  name: string;
  tags?: string[];
  // 根据实际情况添加其他需要的字段
  isFolder: boolean;
  isSpace: boolean;
  isDesktop: boolean;
  type: 'newdoc' | 'modoc' | 'mosheet' | 'table' | 'presentation' | 'form';
  subType?: string;
  starred: boolean;
  updatedAt: number;
  isAdmin?: boolean;
  isFileAdmin?: boolean;
  role?: string;
}
export interface FileAncestors {
  id: number;
  guid: string;
  name: string;
  isFolder: boolean;
  isSpace: boolean;
  isDesktop: boolean;
}

export interface ErrorData {
  code: number;
  msg: string;
  tid: string; // 请求唯一标识
}

export interface ResponseError extends AxiosError {
  data: ErrorData;
}
